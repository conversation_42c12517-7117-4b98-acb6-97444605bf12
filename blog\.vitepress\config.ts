import { defineConfig } from 'vitepress'
import { genFeed } from './genFeed.js'
// @ts-ignore
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  base: '/blog/',
  outDir: '../public/blog', // 路径是相对于 blog 文件夹的
  vite: {
    plugins: [tailwindcss() as any],
  },
  title: 'The Vue Point',
  description: 'The official blog for the Vue.js project',
  cleanUrls: true,
  head: [
    ['meta', { name: 'twitter:site', content: '@vuejs' }],
    ['meta', { name: 'twitter:card', content: 'summary' }],
    [
      'meta',
      {
        name: 'twitter:image',
        content: 'https://vuejs.org/images/logo.png'
      }
    ],

  ],
  buildEnd: genFeed
})
